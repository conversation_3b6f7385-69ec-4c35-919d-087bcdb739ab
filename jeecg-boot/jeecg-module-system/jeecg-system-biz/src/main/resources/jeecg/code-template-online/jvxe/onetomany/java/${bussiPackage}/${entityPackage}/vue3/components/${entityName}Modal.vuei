<#include "/common/utils.ftl">
<template>
<#assign buttonList=[]>
<#if tableVo.extendParams?? && tableVo.extendParams.cgButtonList??>
  <#assign buttonList = tableVo.extendParams.cgButtonList?filter(btn -> btn??)>
</#if>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="${getModalWidth(tableVo.fieldRowNum?default(1))}" @ok="handleSubmit">
    <BasicForm @register="registerForm" ref="formRef" name="${entityName}Form"/>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
<#list subTables as sub><#rt/>
  <#assign refKey = sub.entityName?uncap_first/>
  <#if sub.foreignRelationType =='1'>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <${sub.entityName}Form ref="${sub.entityName?uncap_first}Form" :disabled="formDisabled"></${sub.entityName}Form>
      </a-tab-pane>

  <#else>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="${refKey}"
          :loading="${sub.entityName?uncap_first}Table.loading"
          :columns="${sub.entityName?uncap_first}Table.columns"
          :dataSource="${sub.entityName?uncap_first}Table.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
          />
      </a-tab-pane>
  </#if>
</#list>
    </a-tabs>
    <#if buttonList?? && buttonList?size gt 0>
       <template #insertFooter>
        <#list buttonList as btn>
         <#if btn.buttonStyle=='form'>
           <a-button type="primary" @click="handle${btn.buttonCode?cap_first}" <#if btn.buttonIcon??> preIcon="${btn.buttonIcon}" </#if>>${btn.buttonName}</a-button>
         </#if>
        </#list>
       </template>
    </#if>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    <#list subTables as sub>
      <#if sub.foreignRelationType =='1'>
    import ${sub.entityName}Form from './${sub.entityName}Form.vue'
      </#if>
    </#list>
    import {formSchema<#list subTables as sub><#if sub.foreignRelationType =='0'>,${sub.entityName?uncap_first}Columns</#if></#list>} from '../${entityName}.data';
    import {saveOrUpdate<#list subTables as sub>,${sub.entityName?uncap_first}List</#list>} from '../${entityName}.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    import { useMessage } from '/@/hooks/web/useMessage';
    import { getDateByPicker } from '/@/utils';
    //日期个性化选择
    const fieldPickers = reactive({
    <#list columns as po>
      <#if po.extendParams?exists && po.extendParams.picker?exists>
      ${po.fieldName}: '${po.extendParams.picker}',
      </#if>
    </#list>
    });
    <#list subTables as sub>
      <#if sub.foreignRelationType =='0'>
      const ${sub.entityName?uncap_first}FieldPickers = reactive({
      <#list sub.colums as po>
        <#if po.extendParams?exists && po.extendParams.picker?exists>
        ${po.fieldName}: '${po.extendParams.picker}',
        </#if>
      </#list>
      });
      </#if>
    </#list>
    const { createMessage } = useMessage();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const refKeys = ref([<#list subTables as sub>'${sub.entityName?uncap_first}', </#list>]);
    <#assign hasOne2Many = false>
    <#assign hasOne2One = false>
    const activeKey = ref('${subTables[0].entityName?uncap_first}');
<#list subTables as sub>
<#if sub.foreignRelationType =='0'>
 <#assign hasOne2Many = true>
    const ${sub.entityName?uncap_first} = ref();
</#if>
<#if sub.foreignRelationType =='1'>
 <#assign hasOne2One = true>
    const ${sub.entityName?uncap_first}Form = ref();
</#if>
</#list>
    const tableRefs = {<#list subTables as sub><#if sub.foreignRelationType =='0'>${sub.entityName?uncap_first}, <#assign hasOne2Many = true></#if></#list>};
   <#list subTables as sub>
   <#if sub.foreignRelationType =='0'>
    const ${sub.entityName?uncap_first}Table = reactive({
          loading: false,
          dataSource: [],
          columns:${sub.entityName?uncap_first}Columns
    })
    </#if>
   </#list>
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        <#if tableVo.fieldRowNum == 1>
        labelWidth: 150,
        </#if>
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: ${getFormSpan(tableVo.fieldRowNum?default(1))}}
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
            <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='1'>
             ${sub.entityName?uncap_first}Form.value.initFormData(${sub.entityName?uncap_first}List,data?.record?.id)
            </#if>
            </#list>
            <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='0'>
             requestSubTableData(${sub.entityName?uncap_first}List, {id:data?.record?.id}, ${sub.entityName?uncap_first}Table)
            </#if>
            </#list>
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys<#if hasOne2One==true>,validateSubForm</#if>);

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

    async function reset(){
      await resetFields();
      activeKey.value = '${subTables[0].entityName?uncap_first}';
      <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
      ${sub.entityName?uncap_first}Table.dataSource = [];
        </#if>
        <#if sub.foreignRelationType =='1'>
      ${sub.entityName?uncap_first}Form.value.resetFields();
        </#if>
      </#list>
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
 <#assign subManyIndex = 0>
 <#list subTables as sub><#rt/>
 <#if sub.foreignRelationType =='0'>
           ${sub.entityName?uncap_first}List: allValues.tablesValue[${subManyIndex}].tableData,
           <#assign subManyIndex = subManyIndex+1>
 <#else>
           ${sub.entityName?uncap_first}List: ${sub.entityName?uncap_first}Form.value.getFormData(),
 </#if>
 </#list>
         }
       }
      <#if hasOne2One==true>
     //校验所有一对一子表表单
     function validateSubForm(allValues){
         return new Promise((resolve,reject)=>{
             Promise.all([
             <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='1'>
                  ${sub.entityName?uncap_first}Form.value.validateForm(${sub_index}),
            </#if>
          </#list>
             ]).then(() => {
                 resolve(allValues)
             }).catch(e => {
                 if (e.error === VALIDATE_FAILED) {
                     // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                     activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
                     if (e.errorFields) {
                       const firstField = e.errorFields[0];
                       if (firstField) {
                         e.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
                       }
                     }
                 } else {
                     console.error(e)
                 }
             })
         })
     }
    </#if>
    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            // 预处理日期数据
            changeDateValue(values);
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
      if (formData && fieldPickers) {
          for (let key in fieldPickers) {
              if (formData[key]) {
                  formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
              }
          }
      }
      <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
      if(formData && formData.${sub.entityName?uncap_first}List && formData.${sub.entityName?uncap_first}List.length > 0){
          formData.${sub.entityName?uncap_first}List.forEach(subFormData=>{
              for (let key in ${sub.entityName?uncap_first}FieldPickers) {
                  if (subFormData[key]) {
                      subFormData[key] = getDateByPicker(subFormData[key], ${sub.entityName?uncap_first}FieldPickers[key]);
                  }
              }
          })
      }
        </#if>
      </#list>
    };
<#if buttonList?size gt 0>
  <#list buttonList?sort_by('orderNum') as btn>
      <#if btn.buttonStyle=='form'>
         function handle${btn.buttonCode?cap_first}(){
            createMessage.info('点击了${btn.buttonName}按钮，对应的业务逻辑需自行实现!');
         }
      </#if>
  </#list>
</#if>
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>