import { render } from '@/common/renderUtils';
//列表数据
export const columns = [
 <#list columns as po>
   <#if po.isShowList =='Y' && po.fieldName !='id' && po.fieldName !='delFlag'>
   {
    title: '${po.filedComment}',
    align:"center",
    <#if po.sort=='Y'>
    sorter: true,
    </#if>
    <#if po.classType=='date'>
    dataIndex: '${po.fieldName}',
    <#elseif po.fieldDbType=='Blob'>
    dataIndex: '${po.fieldName}String'
    <#elseif po.classType=='umeditor'>
    dataIndex: '${po.fieldName}',
    <#elseif po.classType=='pca'>
    dataIndex: '${po.fieldName}',
    <#elseif po.classType=='file'>
    dataIndex: '${po.fieldName}',
    <#elseif po.classType=='image'>
    dataIndex: '${po.fieldName}',
    customRender:render.renderImage,
    <#elseif po.classType=='switch'>
    dataIndex: '${po.fieldName}',
    <#assign switch_extend_arr=['Y','N']>
    <#if po.dictField?default("")?contains("[")>
    <#assign switch_extend_arr=po.dictField?eval>
    </#if>
    <#list switch_extend_arr as a>
    <#if a_index == 0>
    <#assign switch_extend_arr1=a>
    <#else>
    <#assign switch_extend_arr2=a>
    </#if>
    </#list>
    customRender:({text}) => {
       return render.renderSwitch(text, [{text:'是',value:'${switch_extend_arr1}'},{text:'否',value:'${switch_extend_arr2}'}])
     },
    <#elseif po.classType == 'sel_tree' || po.classType=='list' || po.classType=='list_multi' || po.classType=='sel_search' || po.classType=='radio' || po.classType=='checkbox' || po.classType=='sel_depart' || po.classType=='sel_user' || po.classType=='popup_dict'>
    dataIndex: '${po.fieldName}_dictText'
    <#else>
    dataIndex: '${po.fieldName}'
    </#if>
   },
   </#if>
 </#list>
];