<#include "/common/utils.ftl">
<template>
  <div class="p-2">
<#assign query_field_no=0>  
<#assign need_category = false>
<#assign need_pca = false>
<#assign need_search = false>
<#assign need_dept_user = false>
<#assign need_switch = false>
<#assign need_dept = false>
<#assign need_multi = false>
<#assign need_popup = false>
<#assign need_popup_dict = false>
<#assign need_select_tag = false>
<#assign need_select_tree = false>
<#assign need_time = false>
<#assign bpm_flag=false>
<#assign need_markdown = false>
<#assign need_upload = false>
<#assign need_image_upload = false>
<#assign need_editor = false>
<#assign need_checkbox = false>
<#assign need_range_number = false>
<#assign is_range = false>
<#assign query_flag = false>
<#assign buttonList=[]>
<#if tableVo.extendParams?? && tableVo.extendParams.cgButtonList??>
  <#assign buttonList = tableVo.extendParams.cgButtonList?filter(btn -> btn??)>
</#if>
<#assign is_like = false>
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
<#-- 开始循环 -->
<#list columns as po>
<#if po.fieldDbName=='bpm_status'>
  <#assign bpm_flag=true>
</#if>
<#if po.classType=='cat_tree' && po.dictText?default("")?trim?length == 0>
<#assign need_category=true>
</#if>
<#if po.classType=='pca'>
<#assign need_pca=true>
</#if>
<#if po.classType=='sel_search'>
<#assign need_search = true>
</#if>
<#if po.classType=='sel_user'>
<#assign need_dept_user = true>
</#if>
<#if po.classType=='sel_depart'>
<#assign need_dept = true>
</#if>
<#if po.classType=='switch'>
<#assign need_switch = true>
</#if>
<#if po.classType=='list_multi'>
<#assign need_multi = true>
</#if>
<#if po.classType=='popup'>
<#assign need_popup = true>
</#if>
<#if po.classType=='popup_dict'>
<#assign need_popup_dict = true>
</#if>
<#if po.classType=='sel_tree'>
<#assign need_select_tree = true>
</#if>
<#if po.classType=='time'>
<#assign need_time = true>
</#if>
<#if po.queryMode=='group' && (po.fieldDbType=='int' || po.fieldDbType=='double' || po.fieldDbType=='BigDecimal')>
<#assign need_range_number = true>
</#if>
<#if po.queryMode=='group'>
<#assign is_range = true>
</#if>
<#if po.queryMode=='like'>
<#assign is_like = true>
</#if>
  <#include "/common/form/native/vue3NativeSearch.ftl">
</#list>
<#if query_field_no gt 2>
          </template>
</#if>
<#if query_flag>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
</#if>
        </a-row>
      </a-form>
    </div>
<#-- 结束循环 -->
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'${entityPackage}:${tableName}:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button  type="primary" v-auth="'${entityPackage}:${tableName}:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button  type="primary" v-auth="'${entityPackage}:${tableName}:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
 <#if buttonList?size gt 0>
    <#list buttonList?sort_by('orderNum') as btn>
       <#if btn.buttonStyle == 'button'>
         <a-button type="primary" @click="handle${btn.buttonCode?cap_first}" <#if btn.buttonIcon??> preIcon="ant-design:${btn.buttonIcon}" </#if>>${btn.buttonName}</a-button>
       </#if>
     </#list>
   </#if>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'${entityPackage}:${tableName}:deleteBatch'">批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <#-- update-begin---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
        <#-- update-end---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      <#list columns as po>
        <#if po.classType=='umeditor' || po.classType=='pca' || po.classType=='file'>
        <template v-if="column.dataIndex==='${po.fieldName}'">
        <#if po.classType=='umeditor'>
          <!--富文本件字段回显插槽-->
          <div v-html="text"></div>
        </#if>
        <#if po.classType=='pca'>
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </#if>
        <#if po.classType=='file'>
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">下载</a-button>
        </#if>
        </template>
        </#if>
      </#list>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <${entityName}Modal ref="registerModal" @success="handleSuccess"></${entityName}Modal>
    <#if bpm_flag==true>
    <!-- 审批记录 -->
    <BpmPictureModal @register="registerBpmModal" />
   </#if>
  </div>
</template>

<script lang="ts" name="${entityPackage}-${entityName?uncap_first}" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './${entityName}.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './${entityName}.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import ${entityName}Modal from './components/${entityName}Modal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
   import {useModal} from '/@/components/Modal';
  <#include "/common/form/native/vue3NativeImport.ftl">
<#if need_category>
  import { loadCategoryData } from '/@/api/common/api';
  import { getAuthCache, setAuthCache } from '/@/utils/auth';
  import { DB_DICT_DATA_KEY } from '/@/enums/cacheEnum';
</#if>
<#if need_pca>
  import { getAreaTextByCode } from '/@/components/Form/src/utils/Area';
</#if>
  import { getDateByPicker } from '/@/utils';
  <#if need_popup_dict>
 import {getPopDictByCode} from "@/utils/dict";
 import {filterMultiDictText} from "@/utils/dict/JDictSelectUtil";
  </#if>
  <#if bpm_flag==true>
  import { startProcess } from '/@/api/common/api';
  const [registerBpmModal, { openModal: bpmPicModal }] = useModal();
  </#if>
  <#if is_range>
  import { cloneDeep } from "lodash-es";
  </#if>

  const fieldPickers = reactive({
  <#list columns as po>
    <#if po.extendParams?exists && po.extendParams.picker?exists>
    ${po.fieldName}: '${po.extendParams.picker}',
    </#if>
  </#list>
  });

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '${tableVo.ftlDescription}',
      api: list,
      columns,
      canResize:true,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: async (params) => {
       <#if is_range>
        let rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
       <#else>
        for (let key in fieldPickers) {
          if (queryParam[key] && fieldPickers[key]) {
            queryParam[key] = getDateByPicker(queryParam[key], fieldPickers[key]);
          }
        }
        return Object.assign(params, queryParam);
       </#if>
      },
      <#if need_popup_dict>
      afterFetch: afterFetch
      </#if>
    },
    exportConfig: {
      name: "${tableVo.ftlDescription}",
      url: getExportUrl,
      <#if is_range>
      params: setRangeQuery,
      <#else>
      params: queryParam,
      </#if>
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

<#-- update-begin---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->
  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }
<#-- update-end---author:chenrui ---date:20231228  for：[QQYUN-7527]vue3代码生成默认带上高级查询---------- -->

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: '${entityPackage}:${tableName}:edit'
      },
    ];
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    <#if bpm_flag==true>
    let dropDownAction = [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: '${entityPackage}:${tableName}:delete'
      },
        {
          label: '审批进度',
          onClick: handlePreviewPic.bind(null, record),
          ifShow: !!record.bpmStatus && record.bpmStatus !== '1',
        }
 <#if buttonList?size gt 0>
     <#list buttonList?sort_by('orderNum') as btn>
        <#if btn.buttonStyle == 'link'>
        ,{
           label: '${btn.buttonName}',
           onClick: handle${btn.buttonCode?cap_first}.bind(null, record),
         }
        </#if>
      </#list>
  </#if>
    ];
    if(record.bpmStatus == '1'){
      dropDownAction.push({
        label: '发起流程',
        popConfirm: {
          title: '确认提交流程吗？',
          confirm: handleProcess.bind(null, record),
          placement: 'topLeft',
        }
      })
    }
    return dropDownAction;
    <#else>
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: '${entityPackage}:${tableName}:delete'
      }
<#if buttonList?size gt 0>
   <#list buttonList?sort_by('orderNum') as btn>
      <#if btn.buttonStyle == 'link'>
      ,{
         label: '${btn.buttonName}',
         onClick: handle${btn.buttonCode?cap_first}.bind(null, record),
       }
      </#if>
    </#list>
</#if>
    ]
    </#if>
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  
  <#if need_popup>
  /**
   *  popup组件值改变事件
   */
  function setFieldsValue(map) {
    Object.keys(map).map((key) => {
      queryParam[key] = map[key];
    });
  }
  </#if>

  <#if need_pca || need_dept_user>
  /**
   * form点击事件(以逗号分割)
   * @param key
   * @param value
   */
  function handleFormJoinChange(key, value) {
    if (typeof value != 'string') {
      queryParam[key] = value.join(',');
    }
  }
  </#if>

  <#if bpm_flag==true>
  /**
   * 提交流程
   */
  async function handleProcess(record) {
    let params = {
      flowCode: 'dev_${tableName}_001',
      id: record.id,
      formUrl: '${entityPackagePath}/components/${entityName}Form',
      formUrlMobile: ''
    }
    await startProcess(params);
    handleSuccess();
  }
   /**
   * 审批进度
   */
  async function handlePreviewPic(record) {
    bpmPicModal(true, {
      flowCode: 'dev_${tableName}_001',
      dataId: record.id,
    });
  }
  </#if>

<#if buttonList?size gt 0>
  <#list buttonList?sort_by('orderNum') as btn>
  <#if btn.buttonStyle=='button'>
     function handle${btn.buttonCode?cap_first}(){
        createMessage.info('点击了${btn.buttonName}按钮，对应的业务逻辑需自行实现!');
     }
  </#if>
   <#if btn.buttonStyle=='link'>
    function handle${btn.buttonCode?cap_first}(record){
      createMessage.info('点击了${btn.buttonName}按钮，对应的业务逻辑需自行实现!');
    }
   </#if>
  </#list>
</#if>

  <#if need_category>
  /**
   * form点击事件
   * @param value
   */
  function handleFormChange(key, value) {
    queryParam[key] = value;
  }
  
  /**
   * 初始化字典配置
   */
  function initDictConfig() {
  <#list columns as po>
  <#if (po.isQuery=='Y' || po.isShowList=='Y') && po.classType!='popup'>
    <#if po.classType=='cat_tree' && need_category==true>
    loadCategoryData({code:'${po.dictField?default("")}'}).then((res) => {
      if (res) {
        const allDictDate = userStore.getAllDictItems;
        if(!allDictDate['${po.dictField?default("")}']){
          userStore.setAllDictItems({...allDictDate,'${po.dictField?default("")}':res});
        }
      }
    });
     </#if>
   </#if>
   </#list>
  }
  initDictConfig();
    </#if>
  <#if is_range>
  
  let rangeField = '${getRangeField(columns)}'
  
  /**
   * 设置范围查询条件
   */
  async function setRangeQuery(){
    let queryParamClone = cloneDeep(queryParam);
    if (rangeField) {
      let fieldsValue = rangeField.split(',');
      fieldsValue.forEach(item => {
        if (queryParamClone[item]) {
          let range = queryParamClone[item];
          queryParamClone[item+'_begin'] = range[0];
          queryParamClone[item+'_end'] = range[1];
          delete queryParamClone[item];
        } else {
          queryParamClone[item+'_begin'] = '';
          queryParamClone[item+'_end'] = '';
        }
      })
    }
    return queryParamClone;
  }
  </#if>

 <#if need_popup_dict>
 /**
  * 翻译Popup字典配置
 */
  async function afterFetch(records){
<#list columns as po>
  <#if po.isQuery=='Y' || po.isShowList=='Y'>
    <#if po.classType=='popup_dict'>
    const ${po.fieldName}Keys = [...new Set(records.map((item) => item['${po.fieldName}']).flatMap((item) => item && item.split(',')))];
    if(${po.fieldName}Keys && ${po.fieldName}Keys.length){
     const dictOptions = await getPopDictByCode(${po.fieldName}Keys.join(','), '${po.dictTable},${po.dictField},${po.dictText}');
     records.forEach((item) => {
       item['${po.fieldName}_dictText'] = filterMultiDictText(dictOptions, item['${po.fieldName}']);
     });
    }
    </#if>
  </#if>
</#list>
   return records;
 }
  </#if>
</script>

<style lang="less" scoped>
<#include "/common/form/native/vueNativeSearchStyle.ftl">
</style>
