package ${bussiPackage}.${entityPackage}.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
import ${bussiPackage}.${entityPackage}.service.I${entityName}Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
<#assign bpm_flag=false>
<#assign has_multi_query_field=false>
<#list originalColumns as po>
<#if po.fieldDbName=='bpm_status'>
  <#assign bpm_flag=true>
</#if>
<#if po.isQuery=='Y' && (po.classType=='list' || po.classType=='list_multi' || po.classType=='radio' || po.classType=='checkbox')>
   <#assign has_multi_query_field=true>
</#if>
</#list>
<#assign enhanceJavaList=[]>
<#if tableVo.extendParams?? && tableVo.extendParams.enhanceJavaList??>
  <#assign enhanceJavaList = tableVo.extendParams.enhanceJavaList?filter(enhance -> enhance??)>
</#if>
 /**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Tag(name="${tableVo.ftlDescription}")
@RestController
@RequestMapping("/${entityPackagePath}/${entityName?uncap_first}")
@Slf4j
public class ${entityName}Controller extends JeecgController<${entityName}, I${entityName}Service> {
	@Autowired
	private I${entityName}Service ${entityName?uncap_first}Service;
	
	/**
	 * 分页列表查询
	 *
	 * @param ${entityName?uncap_first}
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "${tableVo.ftlDescription}-分页列表查询")
	@Operation(summary="${tableVo.ftlDescription}-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<${entityName}>> queryPageList(${entityName} ${entityName?uncap_first},
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

<#if enhanceJavaList?size gt 0>
 <#list enhanceJavaList as enhanceJava>
 <#if enhanceJava.buttonCode=='query' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
       //TODO 查询前触发的方法，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.beforeQuery()
 </#if>
 </#list>
</#if>

    <#if has_multi_query_field>
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
    <#list originalColumns as po>
        <#if po.isQuery=='Y' && (po.classType=='list' || po.classType=='list_multi' || po.classType=='radio' || po.classType=='checkbox')>
        customeRuleMap.put("${po.fieldName}", QueryRuleEnum.LIKE_WITH_OR);
        </#if>
    </#list>
        QueryWrapper<${entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${entityName?uncap_first}, req.getParameterMap(),customeRuleMap);
    <#else>
        QueryWrapper<${entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${entityName?uncap_first}, req.getParameterMap());
    </#if>
		Page<${entityName}> page = new Page<${entityName}>(pageNo, pageSize);
		IPage<${entityName}> pageList = ${entityName?uncap_first}Service.page(page, queryWrapper);
<#if enhanceJavaList?size gt 0>
 <#list enhanceJavaList as enhanceJava>
 <#if enhanceJava.buttonCode=='query' && enhanceJava.event=='end' && enhanceJava.activeStatus=='1'>
      //TODO 查询后触发的方法，代码生成后，请手工实现增强类逻辑;
      //${entityName?uncap_first}Service.afterQuery()
 </#if>
 </#list>
</#if>
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param ${entityName?uncap_first}
	 * @return
	 */
	@AutoLog(value = "${tableVo.ftlDescription}-添加")
	@Operation(summary="${tableVo.ftlDescription}-添加")
	@RequiresPermissions("${entityPackage}:${tableName}:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ${entityName} ${entityName?uncap_first}) {
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='add' && enhanceJava.event=='start'  && enhanceJava.activeStatus=='1'>
      //TODO 新增前的处理方法，代码生成后，请手工实现增强类逻辑;
      //${entityName?uncap_first}Service.beforeAdd()
  </#if>
  </#list>
</#if>
	    <#if bpm_flag>
        ${entityName?uncap_first}.setBpmStatus("1");
	    </#if>
		${entityName?uncap_first}Service.save(${entityName?uncap_first});

<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='add' && enhanceJava.event=='end'  && enhanceJava.activeStatus=='1'>
       //TODO 新增后的处理方法，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.afterAdd()
  </#if>
  </#list>
</#if>
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param ${entityName?uncap_first}
	 * @return
	 */
	@AutoLog(value = "${tableVo.ftlDescription}-编辑")
	@Operation(summary="${tableVo.ftlDescription}-编辑")
	@RequiresPermissions("${entityPackage}:${tableName}:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ${entityName} ${entityName?uncap_first}) {
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='edit' && enhanceJava.event=='start'  && enhanceJava.activeStatus=='1'>
       //TODO 编辑前，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.beforeEdit()
  </#if>
  </#list>
</#if>
		${entityName?uncap_first}Service.updateById(${entityName?uncap_first});
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='edit' && enhanceJava.event=='end' && enhanceJava.activeStatus=='1'>
      //TODO 编辑后，代码生成后，请手工实现增强类逻辑;
      //${entityName?uncap_first}Service.afterEdit()
  </#if>
  </#list>
</#if>
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "${tableVo.ftlDescription}-通过id删除")
	@Operation(summary="${tableVo.ftlDescription}-通过id删除")
	@RequiresPermissions("${entityPackage}:${tableName}:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		${entityName?uncap_first}Service.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "${tableVo.ftlDescription}-批量删除")
	@Operation(summary="${tableVo.ftlDescription}-批量删除")
	@RequiresPermissions("${entityPackage}:${tableName}:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.${entityName?uncap_first}Service.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "${tableVo.ftlDescription}-通过id查询")
	@Operation(summary="${tableVo.ftlDescription}-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<${entityName}> queryById(@RequestParam(name="id",required=true) String id) {
		${entityName} ${entityName?uncap_first} = ${entityName?uncap_first}Service.getById(id);
		if(${entityName?uncap_first}==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(${entityName?uncap_first});
	}

    /**
    * 导出excel
    *
    * @param request
    * @param ${entityName?uncap_first}
    */
    @RequiresPermissions("${entityPackage}:${tableName}:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ${entityName} ${entityName?uncap_first}) {
<#if enhanceJavaList?size gt 0>
 <#list enhanceJavaList as enhanceJava>
 <#if enhanceJava.buttonCode=='export' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
      //TODO 导出前，代码生成后，请手工实现增强类逻辑;
      //${entityName?uncap_first}Service.beforeExport()
 </#if>
 </#list>
</#if>
        return super.exportXls(request, ${entityName?uncap_first}, ${entityName}.class, "${tableVo.ftlDescription}");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("${entityPackage}:${tableName}:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
    <#if enhanceJavaList?size gt 0>
     <#list enhanceJavaList as enhanceJava>
     <#if enhanceJava.buttonCode=='import' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
          //TODO 导入前，代码生成后，请手工实现增强类逻辑;
          //${entityName?uncap_first}Service.beforeImport()
     </#if>
     </#list>
    </#if>
        return super.importExcel(request, response, ${entityName}.class);
    }

}
