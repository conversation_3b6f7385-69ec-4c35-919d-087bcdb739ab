package org.jeecg.modules.demo.emsrepairorder.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.emsrepairorder.entity.EmsRepairOrder;
import org.jeecg.modules.demo.emsrepairorder.service.IEmsRepairOrderService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Tag(name="维修工单表")
@RestController
@RequestMapping("/emsrepairorder/emsRepairOrder")
@Slf4j
public class EmsRepairOrderController extends JeecgController<EmsRepairOrder, IEmsRepairOrderService> {
	@Autowired
	private IEmsRepairOrderService emsRepairOrderService;
	
	/**
	 * 分页列表查询
	 *
	 * @param emsRepairOrder
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "维修工单表-分页列表查询")
	@Operation(summary="维修工单表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsRepairOrder>> queryPageList(EmsRepairOrder emsRepairOrder,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<EmsRepairOrder> queryWrapper = QueryGenerator.initQueryWrapper(emsRepairOrder, req.getParameterMap());
		Page<EmsRepairOrder> page = new Page<EmsRepairOrder>(pageNo, pageSize);
		IPage<EmsRepairOrder> pageList = emsRepairOrderService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param emsRepairOrder
	 * @return
	 */
	@AutoLog(value = "维修工单表-添加")
	@Operation(summary="维修工单表-添加")
	@RequiresPermissions("emsrepairorder:ems_repair_order:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsRepairOrder emsRepairOrder) {
		emsRepairOrderService.save(emsRepairOrder);

		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param emsRepairOrder
	 * @return
	 */
	@AutoLog(value = "维修工单表-编辑")
	@Operation(summary="维修工单表-编辑")
	@RequiresPermissions("emsrepairorder:ems_repair_order:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody EmsRepairOrder emsRepairOrder) {
		emsRepairOrderService.updateById(emsRepairOrder);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "维修工单表-通过id删除")
	@Operation(summary="维修工单表-通过id删除")
	@RequiresPermissions("emsrepairorder:ems_repair_order:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsRepairOrderService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "维修工单表-批量删除")
	@Operation(summary="维修工单表-批量删除")
	@RequiresPermissions("emsrepairorder:ems_repair_order:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsRepairOrderService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "维修工单表-通过id查询")
	@Operation(summary="维修工单表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsRepairOrder> queryById(@RequestParam(name="id",required=true) String id) {
		EmsRepairOrder emsRepairOrder = emsRepairOrderService.getById(id);
		if(emsRepairOrder==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(emsRepairOrder);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsRepairOrder
    */
    @RequiresPermissions("emsrepairorder:ems_repair_order:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsRepairOrder emsRepairOrder) {
        return super.exportXls(request, emsRepairOrder, EmsRepairOrder.class, "维修工单表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsrepairorder:ems_repair_order:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsRepairOrder.class);
    }

}
