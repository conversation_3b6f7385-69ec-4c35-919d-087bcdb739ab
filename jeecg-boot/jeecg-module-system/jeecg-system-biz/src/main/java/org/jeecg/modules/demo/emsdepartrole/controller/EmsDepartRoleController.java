package org.jeecg.modules.demo.emsdepartrole.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.emsdepartrole.entity.EmsDepartRole;
import org.jeecg.modules.demo.emsdepartrole.service.IEmsDepartRoleService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;


import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: ems_depart_role
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Tag(name="ems_depart_role")
@RestController
@RequestMapping("/emsdepartrole/emsDepartRole")
@Slf4j
public class EmsDepartRoleController extends JeecgController<EmsDepartRole, IEmsDepartRoleService> {
	@Autowired
	private IEmsDepartRoleService emsDepartRoleService;

	@Autowired
	private ISysDepartService sysDepartService;

	/**
	 * 分页列表查询
	 *
	 * @param emsDepartRole
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "ems_depart_role-分页列表查询")
	@Operation(summary="ems_depart_role-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsDepartRole>> queryPageList(EmsDepartRole emsDepartRole,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {

        QueryWrapper<EmsDepartRole> queryWrapper = QueryGenerator.initQueryWrapper(emsDepartRole, req.getParameterMap());
		Page<EmsDepartRole> page = new Page<EmsDepartRole>(pageNo, pageSize);
		IPage<EmsDepartRole> pageList = emsDepartRoleService.page(page, queryWrapper);

		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param emsDepartRole
	 * @return
	 */
	@AutoLog(value = "ems_depart_role-添加")
	@Operation(summary="ems_depart_role-添加")
	@RequiresPermissions("emsdepartrole:ems_depart_role:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsDepartRole emsDepartRole) {
		//根据部门id获取部门编码
		SysDepart depart = sysDepartService.getOne(new QueryWrapper<SysDepart>().
				lambda().eq(SysDepart::getId, emsDepartRole.getSysOrgCode()));

		emsDepartRole.setRoleCode(depart.getOrgCode()+emsDepartRole.getRoleCode());
		emsDepartRoleService.save(emsDepartRole);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param emsDepartRole
	 * @return
	 */
	@AutoLog(value = "ems_depart_role-编辑")
	@Operation(summary="ems_depart_role-编辑")
	@RequiresPermissions("emsdepartrole:ems_depart_role:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody EmsDepartRole emsDepartRole) {
		emsDepartRoleService.updateById(emsDepartRole);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "ems_depart_role-通过id删除")
	@Operation(summary="ems_depart_role-通过id删除")
	@RequiresPermissions("emsdepartrole:ems_depart_role:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsDepartRoleService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "ems_depart_role-批量删除")
	@Operation(summary="ems_depart_role-批量删除")
	@RequiresPermissions("emsdepartrole:ems_depart_role:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsDepartRoleService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "ems_depart_role-通过id查询")
	@Operation(summary="ems_depart_role-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsDepartRole> queryById(@RequestParam(name="id",required=true) String id) {
		EmsDepartRole emsDepartRole = emsDepartRoleService.getById(id);
		if(emsDepartRole==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(emsDepartRole);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsDepartRole
    */
    @RequiresPermissions("emsdepartrole:ems_depart_role:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsDepartRole emsDepartRole) {
        return super.exportXls(request, emsDepartRole, EmsDepartRole.class, "ems_depart_role");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsdepartrole:ems_depart_role:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsDepartRole.class);
    }

	/**
	 * 根据部门ID查询角色列表
	 *
	 * @param sysOrgCode 部门ID
	 * @return
	 */
	@Operation(summary="根据部门ID查询角色列表")
	@GetMapping(value = "/queryBySysOrgCode")
	public Result<List<EmsDepartRole>> queryBySysOrgCode(@RequestParam(name="sysOrgCode",required=true) String sysOrgCode) {
		log.info("查询部门角色，部门code {}", sysOrgCode);

		//将部门code转换为部门id
		SysDepart depart = sysDepartService.getOne(new QueryWrapper<SysDepart>()
				.lambda().eq(SysDepart::getOrgCode, sysOrgCode));

		//根据部门id查询所有部门角色
		List<EmsDepartRole> roleList = emsDepartRoleService.list(new QueryWrapper<EmsDepartRole>()
				.lambda().eq(EmsDepartRole::getSysOrgCode, depart.getId()));
		return Result.OK(roleList);
	}

}
