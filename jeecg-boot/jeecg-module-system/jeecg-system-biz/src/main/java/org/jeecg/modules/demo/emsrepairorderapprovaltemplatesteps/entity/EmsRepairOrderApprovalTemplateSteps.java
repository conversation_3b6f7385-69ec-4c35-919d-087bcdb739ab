package org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 审批模板步骤表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Data
@TableName("ems_repair_order_approval_template_steps")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="审批模板步骤表")
public class EmsRepairOrderApprovalTemplateSteps implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**关联审批模板ID*/
	@Excel(name = "关联审批模板ID", width = 15)
    @Schema(description = "关联审批模板ID")
    private java.lang.String templateId;
	/**审批步骤序号*/
	@Excel(name = "审批步骤序号", width = 15)
    @Schema(description = "审批步骤序号")
    private java.lang.String step;
	/**部门角色编码*/
	@Excel(name = "部门角色编码", width = 15)
	@Dict(dictTable = "sys_role", dicText = "role_name", dicCode = "role_code")
    @Schema(description = "部门角色编码")
    private java.lang.String roleCode;
}
