package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.controller;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateTime;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.IEmsRepairOrderApprovalTemplatesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.entity.EmsRepairOrderApprovalTemplateSteps;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.service.IEmsRepairOrderApprovalTemplateStepsService;
import org.jeecg.modules.system.util.SecurityUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Tag(name="审批模板表")
@RestController
@RequestMapping("/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates")
@Slf4j
public class EmsRepairOrderApprovalTemplatesController extends JeecgController<EmsRepairOrderApprovalTemplates, IEmsRepairOrderApprovalTemplatesService> {
	@Autowired
	private IEmsRepairOrderApprovalTemplatesService emsRepairOrderApprovalTemplatesService;

	@Autowired
	private IEmsRepairOrderApprovalTemplateStepsService emsRepairOrderApprovalTemplateStepsService;

	/**
	 * 分页列表查询
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "审批模板表-分页列表查询")
	@Operation(summary="审批模板表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsRepairOrderApprovalTemplates>> queryPageList(EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<EmsRepairOrderApprovalTemplates> queryWrapper = QueryGenerator.initQueryWrapper(emsRepairOrderApprovalTemplates, req.getParameterMap());
		Page<EmsRepairOrderApprovalTemplates> page = new Page<EmsRepairOrderApprovalTemplates>(pageNo, pageSize);
		IPage<EmsRepairOrderApprovalTemplates> pageList = emsRepairOrderApprovalTemplatesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
	@AutoLog(value = "审批模板表-添加")
	@Operation(summary="审批模板表-添加")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
		// 新增时默认设置为未激活状态
		if (emsRepairOrderApprovalTemplates.getIsActive() == null || emsRepairOrderApprovalTemplates.getIsActive().isEmpty()) {
			emsRepairOrderApprovalTemplates.setIsActive("0");
		}

		// 设置创建时间和更新时间
		Date now = new Date();
		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();


		emsRepairOrderApprovalTemplates.setCreateTime(now);
		emsRepairOrderApprovalTemplates.setUpdateTime(now);
		emsRepairOrderApprovalTemplates.setCreateBy(currentUser);
		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);

		emsRepairOrderApprovalTemplatesService.save(emsRepairOrderApprovalTemplates);


		// 设置具体的审批模板步骤
		List<EmsRepairOrderApprovalTemplateSteps> steps = new ArrayList<>();
//		List<String> selectedRoleIds = emsRepairOrderApprovalTemplates.getSelectedRoleIds();
		List<Object> selectedRoles = emsRepairOrderApprovalTemplates.getSelectedRoles();
		for (Object selectedRole : selectedRoles) {
			LinkedHashMap role = (LinkedHashMap) selectedRole;
			EmsRepairOrderApprovalTemplateSteps step = new EmsRepairOrderApprovalTemplateSteps();
			step.setRoleCode(role.get("roleCode").toString());
			step.setStep(role.get("sortOrder").toString());
			step.setSysOrgCode(emsRepairOrderApprovalTemplates.getSysOrgCode());

			step.setTemplateId(emsRepairOrderApprovalTemplates.getId());

			steps.add(step);
		}

		emsRepairOrderApprovalTemplateStepsService.saveBatch(steps);


		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
//	@AutoLog(value = "审批模板表-编辑")
//	@Operation(summary="审批模板表-编辑")
//	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:edit")
//	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
//	public Result<String> edit(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
//		// 设置更新时间和更新人
//		// 设置创建时间和更新时间
//		Date now = new Date();
//		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();
//
//
//		emsRepairOrderApprovalTemplates.setUpdateTime(now);
//		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);
//		emsRepairOrderApprovalTemplatesService.updateById(emsRepairOrderApprovalTemplates);
//		return Result.OK("编辑成功!");
//	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "审批模板表-通过id删除")
	@Operation(summary="审批模板表-通过id删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsRepairOrderApprovalTemplatesService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "审批模板表-批量删除")
	@Operation(summary="审批模板表-批量删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsRepairOrderApprovalTemplatesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "审批模板表-通过id查询")
	@Operation(summary="审批模板表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsRepairOrderApprovalTemplates> queryById(@RequestParam(name="id",required=true) String id) {
		EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates = emsRepairOrderApprovalTemplatesService.getById(id);
		if(emsRepairOrderApprovalTemplates==null) {
			return Result.error("未找到对应数据");
		}

		// 查询审批步骤，按步骤序号排序
		List<EmsRepairOrderApprovalTemplateSteps> steps = emsRepairOrderApprovalTemplateStepsService.list(
			new QueryWrapper<EmsRepairOrderApprovalTemplateSteps>()
				.lambda()
				.eq(EmsRepairOrderApprovalTemplateSteps::getTemplateId, id)
				.orderByAsc(EmsRepairOrderApprovalTemplateSteps::getStep)
		);

		// 初始化selectedRoles列表
		if (emsRepairOrderApprovalTemplates.getSelectedRoles() == null) {
			emsRepairOrderApprovalTemplates.setSelectedRoles(new java.util.ArrayList<>());
		}

		// 构建审批步骤信息
		steps.forEach(step -> {
			HashMap<String, Object> stepMap = new HashMap<>();
			stepMap.put("roleCode", step.getRoleCode());
			stepMap.put("step", step.getStep());
			stepMap.put("sortOrder", Integer.parseInt(step.getStep())); // 用于前端排序

			// 查询角色名称
			try {
				// 这里需要通过roleCode查询角色名称
				// 由于@Dict注解在查询列表时会自动转换，但在单个查询时需要手动处理
				// 可以通过系统角色服务查询角色名称
				stepMap.put("roleName", ""); // 暂时留空，前端可以通过roleCode_dictText获取
			} catch (Exception e) {
				log.warn("查询角色名称失败: {}", e.getMessage());
				stepMap.put("roleName", "");
			}

			emsRepairOrderApprovalTemplates.getSelectedRoles().add(stepMap);
		});

		return Result.OK(emsRepairOrderApprovalTemplates);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsRepairOrderApprovalTemplates
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
        return super.exportXls(request, emsRepairOrderApprovalTemplates, EmsRepairOrderApprovalTemplates.class, "审批模板表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsRepairOrderApprovalTemplates.class);
    }

}
