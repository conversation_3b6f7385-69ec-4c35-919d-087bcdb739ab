package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.controller;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateTime;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.emsdepartrole.entity.EmsDepartRole;
import org.jeecg.modules.demo.emsdepartrole.service.IEmsDepartRoleService;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.IEmsRepairOrderApprovalTemplatesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.entity.EmsRepairOrderApprovalTemplateSteps;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplatesteps.service.IEmsRepairOrderApprovalTemplateStepsService;
import org.jeecg.modules.system.util.SecurityUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
 /**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Tag(name="审批模板表")
@RestController
@RequestMapping("/emsrepairorderapprovaltemplates/emsRepairOrderApprovalTemplates")
@Slf4j
public class EmsRepairOrderApprovalTemplatesController extends JeecgController<EmsRepairOrderApprovalTemplates, IEmsRepairOrderApprovalTemplatesService> {
	@Autowired
	private IEmsRepairOrderApprovalTemplatesService emsRepairOrderApprovalTemplatesService;

	@Autowired
	private IEmsRepairOrderApprovalTemplateStepsService emsRepairOrderApprovalTemplateStepsService;

	@Autowired
	private IEmsDepartRoleService emsDepartRoleService;

	/**
	 * 分页列表查询
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "审批模板表-分页列表查询")
	@Operation(summary="审批模板表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<EmsRepairOrderApprovalTemplates>> queryPageList(EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {


        QueryWrapper<EmsRepairOrderApprovalTemplates> queryWrapper = QueryGenerator.initQueryWrapper(emsRepairOrderApprovalTemplates, req.getParameterMap());
		Page<EmsRepairOrderApprovalTemplates> page = new Page<EmsRepairOrderApprovalTemplates>(pageNo, pageSize);
		IPage<EmsRepairOrderApprovalTemplates> pageList = emsRepairOrderApprovalTemplatesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
	@AutoLog(value = "审批模板表-添加")
	@Operation(summary="审批模板表-添加")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
		// 新增时默认设置为未激活状态
		if (emsRepairOrderApprovalTemplates.getIsActive() == null || emsRepairOrderApprovalTemplates.getIsActive().isEmpty()) {
			emsRepairOrderApprovalTemplates.setIsActive("0");
		}

		// 设置创建时间和更新时间
		Date now = new Date();
		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();


		emsRepairOrderApprovalTemplates.setCreateTime(now);
		emsRepairOrderApprovalTemplates.setUpdateTime(now);
		emsRepairOrderApprovalTemplates.setCreateBy(currentUser);
		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);

		emsRepairOrderApprovalTemplatesService.save(emsRepairOrderApprovalTemplates);


		// 设置具体的审批模板步骤
		List<EmsRepairOrderApprovalTemplateSteps> steps = new ArrayList<>();
//		List<String> selectedRoleIds = emsRepairOrderApprovalTemplates.getSelectedRoleIds();
		List<Object> selectedRoles = emsRepairOrderApprovalTemplates.getSelectedRoles();
		for (Object selectedRole : selectedRoles) {
			LinkedHashMap role = (LinkedHashMap) selectedRole;
			EmsRepairOrderApprovalTemplateSteps step = new EmsRepairOrderApprovalTemplateSteps();
			step.setRoleCode(role.get("roleCode").toString());
			step.setStep(role.get("sortOrder").toString());
			step.setSysOrgCode(emsRepairOrderApprovalTemplates.getSysOrgCode());

			step.setTemplateId(emsRepairOrderApprovalTemplates.getId());

			steps.add(step);
		}

		emsRepairOrderApprovalTemplateStepsService.saveBatch(steps);


		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param emsRepairOrderApprovalTemplates
	 * @return
	 */
//	@AutoLog(value = "审批模板表-编辑")
//	@Operation(summary="审批模板表-编辑")
//	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:edit")
//	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
//	public Result<String> edit(@RequestBody EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
//		// 设置更新时间和更新人
//		// 设置创建时间和更新时间
//		Date now = new Date();
//		String currentUser = ((LoginUser) SecurityUtils.getSubject().getPrincipal()).getUsername();
//
//
//		emsRepairOrderApprovalTemplates.setUpdateTime(now);
//		emsRepairOrderApprovalTemplates.setUpdateBy(currentUser);
//		emsRepairOrderApprovalTemplatesService.updateById(emsRepairOrderApprovalTemplates);
//		return Result.OK("编辑成功!");
//	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "审批模板表-通过id删除")
	@Operation(summary="审批模板表-通过id删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		emsRepairOrderApprovalTemplatesService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "审批模板表-批量删除")
	@Operation(summary="审批模板表-批量删除")
	@RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.emsRepairOrderApprovalTemplatesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "审批模板表-通过id查询")
	@Operation(summary="审批模板表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<EmsRepairOrderApprovalTemplates> queryById(@RequestParam(name="id",required=true) String id) {
			log.info("查询审批模板详情，ID: {}", id);

			EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates = emsRepairOrderApprovalTemplatesService.getById(id);
			if(emsRepairOrderApprovalTemplates==null) {
				log.warn("未找到ID为{}的审批模板", id);
				return Result.error("未找到对应数据");
			}

			log.info("找到审批模板: {}", emsRepairOrderApprovalTemplates.getTemplateName());

		// 查询审批步骤，按步骤序号排序
		List<EmsRepairOrderApprovalTemplateSteps> steps = emsRepairOrderApprovalTemplateStepsService.list(
			new QueryWrapper<EmsRepairOrderApprovalTemplateSteps>()
				.lambda()
				.eq(EmsRepairOrderApprovalTemplateSteps::getTemplateId, id)
				.orderByAsc(EmsRepairOrderApprovalTemplateSteps::getStep)
		);

		log.info("查询到{}个审批步骤", steps != null ? steps.size() : 0);

		// 初始化selectedRoles列表
		List<Object> selectedRoles = new ArrayList<>();
		emsRepairOrderApprovalTemplates.setSelectedRoles(selectedRoles);

		// 构建审批步骤信息
		if (steps != null && !steps.isEmpty()) {
			for (EmsRepairOrderApprovalTemplateSteps step : steps) {
				HashMap<String, Object> stepMap = new HashMap<>();
				stepMap.put("roleCode", step.getRoleCode());
				stepMap.put("step", step.getStep());

				// 查询角色名称 todo: 前端可以用注解查询字典的，但是我不了解，暂时直接手动查询了。
				EmsDepartRole one = emsDepartRoleService.getOne(new QueryWrapper<EmsDepartRole>()
						.lambda()
						.eq(EmsDepartRole::getRoleCode, step.getRoleCode()));
				stepMap.put("roleName", one.getRoleName());

				selectedRoles.add(stepMap);
			}
		}

			log.info("成功查询审批模板详情，包含{}个审批步骤", selectedRoles.size());
			return Result.OK(emsRepairOrderApprovalTemplates);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param emsRepairOrderApprovalTemplates
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, EmsRepairOrderApprovalTemplates emsRepairOrderApprovalTemplates) {
        return super.exportXls(request, emsRepairOrderApprovalTemplates, EmsRepairOrderApprovalTemplates.class, "审批模板表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("emsrepairorderapprovaltemplates:ems_repair_order_approval_templates:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, EmsRepairOrderApprovalTemplates.class);
    }

}
