package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Data
@TableName("ems_repair_order_approval_templates")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="审批模板表")
public class EmsRepairOrderApprovalTemplates implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**模板名称*/
	@Excel(name = "模板名称", width = 15)
    @Schema(description = "模板名称")
    private java.lang.String templateName;
	/**是否激活（1=激活，0=未激活）*/
	@Excel(name = "是否激活（1=激活，0=未激活）", width = 15)
    @Schema(description = "是否激活（1=激活，0=未激活）")
    private java.lang.String isActive;
	/**说明*/
	@Excel(name = "说明", width = 15)
    @Schema(description = "说明")
    private java.lang.String remark;

    /**选中的部门ID*/
    @TableField(exist = false)
    @Schema(description = "选中的部门ID")
    private java.lang.String selectedDepartId;

    /**选中的部门名称*/
    @TableField(exist = false)
    @Schema(description = "选中的部门名称")
    private java.lang.String selectedDepartName;

    /**选中的角色ID列表（JSON格式）*/
    @TableField(exist = false)
    @Schema(description = "选中的角色ID列表")
    private java.util.List<String> selectedRoleIds;

    /**选中的角色详细信息（JSON格式）*/
    @TableField(exist = false)
    @Schema(description = "选中的角色详细信息")
    private java.util.List<Object> selectedRoles;
}
