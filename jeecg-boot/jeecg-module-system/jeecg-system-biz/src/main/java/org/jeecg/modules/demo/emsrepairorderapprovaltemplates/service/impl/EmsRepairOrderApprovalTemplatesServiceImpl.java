package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.impl;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.mapper.EmsRepairOrderApprovalTemplatesMapper;
import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service.IEmsRepairOrderApprovalTemplatesService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Service
public class EmsRepairOrderApprovalTemplatesServiceImpl extends ServiceImpl<EmsRepairOrderApprovalTemplatesMapper, EmsRepairOrderApprovalTemplates> implements IEmsRepairOrderApprovalTemplatesService {

}
