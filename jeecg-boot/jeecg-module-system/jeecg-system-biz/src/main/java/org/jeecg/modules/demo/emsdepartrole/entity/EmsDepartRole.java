package org.jeecg.modules.demo.emsdepartrole.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ems_depart_role
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Data
@TableName("ems_depart_role")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="ems_depart_role")
public class EmsDepartRole implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private java.lang.String id;
	/**创建人*/
    @Schema(description = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
	@Dict(dictTable = "sys_depart", dicText = "depart_name", dicCode = "id")
    @Schema(description = "所属部门")
    private java.lang.String sysOrgCode;
	/**部门角色名称*/
	@Excel(name = "部门角色名称", width = 15)
    @Schema(description = "部门角色名称")
    private java.lang.String roleName;
	/**关联sys_role中角色id*/
	@Excel(name = "关联sys_role中角色id", width = 15)
	@Dict(dictTable = "sys_role", dicText = "role_name", dicCode = "id")
    @Schema(description = "关联sys_role中角色id")
    private java.lang.String roleId;
	/**部门角色编码*/
	@Excel(name = "部门角色编码", width = 15)
    @Schema(description = "部门角色编码")
    private java.lang.String roleCode;
	/**描述*/
	@Excel(name = "描述", width = 15)
    @Schema(description = "描述")
    private java.lang.String description;
}
