create table ems_depart_role (
                                 id varchar(36) not null,
                                 create_by varchar(50) comment '创建人',
                                 create_time datetime comment '创建日期',
                                 update_by varchar(50) comment '更新人',
                                 update_time datetime comment '更新日期',
                                 sys_org_code varchar(64) not null comment '所属部门',
                                 role_name varchar(200) not null comment '部门角色名称',
                                 role_id varchar(32) not null comment '关联sys_role中角色id',
                                 role_code varchar(100) not null comment '部门角色编码',
                                 description varchar(255) comment '描述',
                                 primary key (id)
) engine=InnoDB;

create table ems_repair_order
(
    id                   varchar(36)             not null
        primary key,
    create_by            varchar(50)             null comment '创建人',
    create_time          datetime                null comment '创建日期',
    update_by            varchar(50)             null comment '更新人',
    update_time          datetime                null comment '更新日期',
    sys_org_code         varchar(64)             null comment '所属部门',
    equipment_id         varchar(36)             not null comment '关联设备表',
    report_id            varchar(36)             not null comment '发起人id',
    principal_id         varchar(36)             null comment '负责人id',
    fault_description    varchar(500)            not null comment '故障描述',
    attachment           varchar(1000)           null comment '故障图片',
    fault_attachment     varchar(1000)           null comment '故障附件',
    fault_title          varchar(50)             not null comment '故障标题',
    handle_images        varchar(1000)           null comment '处理图片',
    handle_attachment    varchar(1000)           null comment '处理附件',
    approval_progress    varchar(10) default '0' null comment '当前审批进度（已完成的步骤数）',
    total_approval_steps varchar(10)             not null comment '总审批步骤数（线性审批的总人数）'
);

create table ems_repair_order_audit_logs
(
    id            varchar(36) not null
        primary key,
    create_by     varchar(50) null comment '创建人',
    create_time   datetime    null comment '创建日期',
    update_by     varchar(50) null comment '更新人',
    update_time   datetime    null comment '更新日期',
    sys_org_code  varchar(64) null comment '所属部门',
    order_id      varchar(36) not null comment '关联工单ID',
    operator_id   varchar(36) not null comment '操作人ID',
    operator_type varchar(32) not null comment '操作人角色(0:发起人 1:审核人 2:负责人)',
    action_type   varchar(32) not null comment '操作类型1 = 提交工单 2 = 审核通过 3 = 审核驳回 4 = 重新提交 5 = 处理中 6 = 处理完成 7 = 关闭工单 8 = 其他操作',
    remark        varchar(1500) null comment '操作备注'
);

-- 审批模板表：存储模板元信息，不直接存储JSON（JSON解析后存入步骤表）
create table ems_repair_order_approval_templates (
                                                     id varchar(36) not null,
                                                     create_by varchar(50) not null comment '创建人',
                                                     create_time datetime not null comment '创建日期',
                                                     update_by varchar(50) not null comment '更新人',
                                                     update_time datetime not null comment '更新日期',
                                                     sys_org_code varchar(64) not null comment '所属部门',
                                                     template_name varchar(200) not null comment '模板名称',
                                                     is_active varchar(32) default 0 not null comment '是否激活（1=激活，0=未激活）',
                                                     remark varchar(3200) comment '说明',
                                                     primary key (id)
) engine=InnoDB;

# 审批模板步骤表（解析模板JSON生成）
create table ems_repair_order_approval_template_steps
(
    id           varchar(36) not null
        primary key,
    create_by    varchar(50) null comment '创建人',
    create_time  datetime    not null comment '审批时间',
    update_by    varchar(50) not null comment '更新人',
    update_time  datetime    null comment '更新日期',
    sys_org_code varchar(64) not null comment '所属部门',
    template_id     varchar(36)  not null comment '关联审批模板ID',
    step         varchar(10) not null comment '审批步骤序号',
    role_code varchar(2000) not null comment '角色编码'
);
