<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :width="800"
    :showOkBtn="false"
    :showCancelBtn="false"
    :closable="true"
    @cancel="closeModal"
  >
    <div class="approval-template-detail-modal">
      <a-spin :spinning="loading">
        <EmsRepairOrderApprovalTemplatesDetail 
          v-if="templateId" 
          :templateId="templateId" 
        />
      </a-spin>
    </div>
    
    <template #footer>
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import EmsRepairOrderApprovalTemplatesDetail from './EmsRepairOrderApprovalTemplatesDetail.vue';

const emit = defineEmits(['success', 'register']);

// 状态
const loading = ref(false);
const templateId = ref('');
const templateName = ref('');

// 计算属性
const modalTitle = computed(() => {
  return templateName.value ? `审批模板详情 - ${templateName.value}` : '审批模板详情';
});

// 注册Modal
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  loading.value = true;
  
  try {
    if (data?.record) {
      templateId.value = data.record.id;
      templateName.value = data.record.templateName || '';
    }
  } catch (error) {
    console.error('初始化详情Modal失败:', error);
  } finally {
    loading.value = false;
  }
});
</script>

<style lang="less" scoped>
.approval-template-detail-modal {
  min-height: 400px;
}
</style>
