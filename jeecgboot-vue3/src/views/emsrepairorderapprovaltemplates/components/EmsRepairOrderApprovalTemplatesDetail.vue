<template>
  <div class="approval-template-detail">
    <a-card title="审批模板详情" class="detail-card">
      <!-- 基本信息 -->
      <div class="basic-info">
        <a-row :gutter="16">
          <a-col :span="12">
            <div class="info-item">
              <span class="label">模板名称：</span>
              <span class="value">{{ templateData.templateName }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <span class="label">激活状态：</span>
              <a-tag :color="templateData.isActive === '1' ? 'green' : 'orange'">
                {{ templateData.isActive === '1' ? '激活' : '未激活' }}
              </a-tag>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <div class="info-item">
              <span class="label">创建人：</span>
              <span class="value">{{ templateData.createBy }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(templateData.createTime) }}</span>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="12">
            <div class="info-item">
              <span class="label">更新人：</span>
              <span class="value">{{ templateData.updateBy }}</span>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="info-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ formatDate(templateData.updateTime) }}</span>
            </div>
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px;" v-if="templateData.remark">
          <a-col :span="24">
            <div class="info-item">
              <span class="label">说明：</span>
              <div class="value remark">{{ templateData.remark }}</div>
            </div>
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <!-- 审批流程 -->
      <div class="approval-flow">
        <h4 class="section-title">
          <Icon icon="ant-design:flow-chart-outlined" />
          审批流程
        </h4>

        <div class="flow-container" v-if="approvalSteps.length > 0">
          <div
            v-for="(step, index) in approvalSteps"
            :key="index"
            class="flow-step"
          >
            <div class="step-number">{{ step.step }}</div>
            <div class="step-content">
              <div class="step-title">第{{ step.step }}步</div>
              <div class="step-role">
                <Icon icon="ant-design:user-outlined" />
                <span class="role-code">{{ step.roleCode }}</span>
                <span v-if="step.roleName" class="role-name">（{{ step.roleName }}）</span>
              </div>
            </div>
            <div v-if="index < approvalSteps.length - 1" class="step-arrow">
              <Icon icon="ant-design:arrow-down-outlined" />
            </div>
          </div>
        </div>

        <a-empty v-else description="暂无审批流程配置" />
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Icon } from '/@/components/Icon';
import { useMessage } from '/@/hooks/web/useMessage';
import { queryById } from '../EmsRepairOrderApprovalTemplates.api';
import { formatToDateTime } from '/@/utils/dateUtil';

const props = defineProps({
  templateId: {
    type: String,
    required: true
  }
});

const { createMessage } = useMessage();

// 模板数据
const templateData = ref<any>({});
const loading = ref(false);

// 审批步骤数据
const approvalSteps = computed(() => {
  if (!templateData.value.selectedRoles) return [];

  return templateData.value.selectedRoles
    .sort((a, b) => {
      // 按步骤序号排序，支持数字和字符串格式
      const stepA = parseInt(a.sortOrder || a.step || '0');
      const stepB = parseInt(b.sortOrder || b.step || '0');
      return stepA - stepB;
    })
    .map(step => ({
      ...step,
      step: step.sortOrder || step.step || '1', // 使用 sortOrder 作为步骤号
      sortOrder: parseInt(step.sortOrder || step.step || '1')
    }));
});

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '-';
  return formatToDateTime(date);
};

// 查询模板详情
const queryTemplateDetail = async () => {
  try {
    loading.value = true;
    console.log('开始查询模板详情，ID:', props.templateId);

    const result = await queryById(props.templateId);
    console.log('API响应结果:', result);

    if (result && result.success) {
      templateData.value = result.result;
      console.log('模板详情数据:', result.result);
      console.log('审批步骤数据:', result.result.selectedRoles);
      createMessage.success('模板详情加载成功');
    } else {
      console.error('API返回失败:', result);
      createMessage.error(result?.message || '查询模板详情失败');
    }
  } catch (error) {
    console.error('查询模板详情异常:', error);
    createMessage.error('查询模板详情失败: ' + error.message);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.templateId) {
    queryTemplateDetail();
  }
});
</script>

<style lang="less" scoped>
.approval-template-detail {
  .detail-card {
    .basic-info {
      .info-item {
        margin-bottom: 8px;

        .label {
          font-weight: 500;
          color: #666;
          min-width: 80px;
          display: inline-block;
        }

        .value {
          color: #333;

          &.remark {
            margin-top: 8px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }

    .approval-flow {
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .flow-container {
        .flow-step {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #1890ff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            flex-shrink: 0;
          }

          .step-content {
            margin-left: 16px;
            flex: 1;

            .step-title {
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
            }

            .step-role {
              display: flex;
              align-items: center;
              gap: 4px;
              color: #666;

              .role-code {
                font-weight: 500;
                color: #333;
              }

              .role-name {
                color: #1890ff;
                font-style: italic;
              }
            }
          }

          .step-arrow {
            margin-left: 24px;
            color: #ccc;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
