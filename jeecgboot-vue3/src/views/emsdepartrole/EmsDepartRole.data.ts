import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属部门',
    align:"center",
    dataIndex: 'sysOrgCode_dictText'
   },
   {
    title: '部门角色名称',
    align:"center",
    dataIndex: 'roleName'
   },
   {
    title: '关联角色',
    align:"center",
    dataIndex: 'roleId_dictText'
   },
   {
    title: '部门角色编码',
    align:"center",
    dataIndex: 'roleCode'
   },
   {
    title: '描述',
    align:"center",
    dataIndex: 'description'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属部门',
    field: 'sysOrgCode',
     component: 'JSelectDept',
     componentProps:{
      },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属部门!'},
          ];
     },
  },
  {
    label: '部门角色名称',
    field: 'roleName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入部门角色名称!'},
          ];
     },
  },
  {
    label: '关联sys_role中角色id',
    field: 'roleId',
    component: 'JSelectRole',
    componentProps: {
      //是否单选,默认false
      isRadioSelection: true,
      //角色标题
      modalTitle: '选择角色',
      //取值字段配置,一般为主键字段
      rowKey: 'id',
      //显示字段配置
      labelKey: 'roleName',
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择关联的角色!'},
          ];
     },
  },
  {
    label: '部门角色编码',
    field: 'roleCode',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入部门角色编码!'},
          ];
     },
  },
  {
    label: '描述',
    field: 'description',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sysOrgCode: {title: '所属部门',order: 0,view: 'sel_depart', type: 'string',},
  roleName: {title: '部门角色名称',order: 1,view: 'text', type: 'string',},
  roleId: {title: '关联sys_role中角色id',order: 2,view: 'text', type: 'string',},
  roleCode: {title: '部门角色编码',order: 3,view: 'text', type: 'string',},
  description: {title: '描述',order: 4,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
